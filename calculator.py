import tkinter as tk
import re

# Global state variables
memory = 0
should_clear_on_next_input = False
last_input_was_memory_add = False

def append_to_display(value):
    """Append value to display with proper state management"""
    global should_clear_on_next_input, last_input_was_memory_add

    current = entry.get()

    if current == 'Error':
        entry.delete(0, tk.END)
        current = ''

    is_operator = str(value) in ['+', '-', '*', '/']

    if last_input_was_memory_add or should_clear_on_next_input:
        if not is_operator:
            entry.delete(0, tk.END)
            current = ''
        last_input_was_memory_add = False
        should_clear_on_next_input = False

    # Prevent multiple decimal points in a single number
    if str(value) == '.' and '.' in current.split('+/-*/')[-1]:
        return

    entry.delete(0, tk.END)
    entry.insert(tk.END, current + str(value))

def button_click(number):
    """Handle number and operator button clicks"""
    append_to_display(number)

def button_clear():
    """Clear display and reset all state"""
    global should_clear_on_next_input, last_input_was_memory_add
    entry.delete(0, tk.END)
    should_clear_on_next_input = False
    last_input_was_memory_add = False

def button_equal():
    """Calculate the expression in the display"""
    global should_clear_on_next_input, last_input_was_memory_add

    expression = entry.get()
    if not expression or expression[-1] in ['+', '-', '*', '/']:
        return

    try:
        result = eval(expression)
        if isinstance(result, (int, float)):
            # Format the number to remove trailing zeros
            display_num = str(float(result))
            if display_num.endswith('.0'):
                display_num = display_num[:-2]
            entry.delete(0, tk.END)
            entry.insert(tk.END, display_num)
        else:
            entry.delete(0, tk.END)
            entry.insert(tk.END, "Error")
        should_clear_on_next_input = True
        last_input_was_memory_add = False
    except:
        entry.delete(0, tk.END)
        entry.insert(tk.END, "Error")

def button_percentage():
    """Improved percentage function with three modes"""
    global should_clear_on_next_input, last_input_was_memory_add

    try:
        current_display = entry.get()

        # Case 1: Simple number followed by % (e.g., "50" -> "0.5")
        if re.match(r'^[\d.]+$', current_display):
            number = float(current_display)
            percent_value = number / 100
            # Format the number to remove trailing zeros
            display_num = str(float(percent_value))
            if display_num.endswith('.0'):
                display_num = display_num[:-2]
            entry.delete(0, tk.END)
            entry.insert(tk.END, display_num)
            should_clear_on_next_input = True
            last_input_was_memory_add = False
            return

        # Case 2: Expression ending with operator and number (e.g., "100+20" -> "100*(1+20/100)")
        match = re.search(r'^(.+)([\+\-\*\/])([\d.]+)$', current_display)
        if match:
            base_expr = match.group(1)
            operator = match.group(2)
            last_number = float(match.group(3))

            if operator == '+':
                # num1 + num2 % -> num1 * (1 + num2/100)
                percent_multiplier = 1 + (last_number / 100)
                new_expr = f"{base_expr}*{percent_multiplier}"
            elif operator == '-':
                # num1 - num2 % -> num1 * (1 - num2/100)
                percent_multiplier = 1 - (last_number / 100)
                new_expr = f"{base_expr}*{percent_multiplier}"
            elif operator == '*':
                # num1 * num2 % -> num1 * (num2/100)
                percent_value = last_number / 100
                new_expr = f"{base_expr}*{percent_value}"
            elif operator == '/':
                # num1 / num2 % -> num1 / (num2/100)
                percent_value = last_number / 100
                new_expr = f"{base_expr}/{percent_value}"

            entry.delete(0, tk.END)
            entry.insert(tk.END, new_expr)
            should_clear_on_next_input = True
            last_input_was_memory_add = False
            return

        # Case 3: Fallback - just convert the last number to percentage
        match = re.search(r'([\d.]+)$', current_display)
        if match:
            last_number = float(match.group(1))
            prefix = current_display[:-len(match.group(1))]
            percent_value = last_number / 100
            # Format the number to remove trailing zeros
            display_num = str(float(percent_value))
            if display_num.endswith('.0'):
                display_num = display_num[:-2]
            entry.delete(0, tk.END)
            entry.insert(tk.END, f"{prefix}{display_num}")
            should_clear_on_next_input = True
            last_input_was_memory_add = False
    except:
        entry.delete(0, tk.END)
        entry.insert(tk.END, "Error")

def button_memory_add():
    """Add current display value to memory"""
    global memory, should_clear_on_next_input, last_input_was_memory_add

    try:
        current_display = entry.get()

        # If there's an expression, evaluate it first
        if any(op in current_display for op in ['+', '-', '*', '/']):
            button_equal()
            current_display = entry.get()

        # Add to memory
        if current_display and current_display != 'Error':
            memory += float(current_display)
            last_input_was_memory_add = True
            should_clear_on_next_input = True
    except:
        entry.delete(0, tk.END)
        entry.insert(tk.END, "Error")

def button_memory_recall():
    """Recall value from memory"""
    global should_clear_on_next_input, last_input_was_memory_add

    # Format the number to remove trailing zeros
    display_num = str(float(memory))
    if display_num.endswith('.0'):
        display_num = display_num[:-2]
    entry.delete(0, tk.END)
    entry.insert(tk.END, display_num)
    should_clear_on_next_input = True
    last_input_was_memory_add = False

def button_memory_clear():
    """Clear memory"""
    global memory
    memory = 0

# Main window
window = tk.Tk()
window.title("Advanced Calculator")
window.configure(bg='lightgray')

# Input field
entry = tk.Entry(window, width=25, borderwidth=3, font=('Arial', 14), justify='right')
entry.grid(row=0, column=0, columnspan=5, padx=10, pady=10, sticky='ew')

# Configure column weights for responsive layout
for i in range(5):
    window.grid_columnconfigure(i, weight=1)

# Memory and function buttons (Row 1)
btn_mc = tk.Button(window, text="MC", padx=15, pady=15, command=button_memory_clear, bg='lightblue')
btn_mr = tk.Button(window, text="MR", padx=15, pady=15, command=button_memory_recall, bg='lightblue')
btn_m_plus = tk.Button(window, text="M+", padx=15, pady=15, command=button_memory_add, bg='lightblue')
btn_percent = tk.Button(window, text="%", padx=15, pady=15, command=button_percentage, bg='orange')
btn_clear = tk.Button(window, text="C", padx=15, pady=15, command=button_clear, bg='red', fg='white')

btn_mc.grid(row=1, column=0, padx=2, pady=2, sticky='nsew')
btn_mr.grid(row=1, column=1, padx=2, pady=2, sticky='nsew')
btn_m_plus.grid(row=1, column=2, padx=2, pady=2, sticky='nsew')
btn_percent.grid(row=1, column=3, padx=2, pady=2, sticky='nsew')
btn_clear.grid(row=1, column=4, padx=2, pady=2, sticky='nsew')

# Number and operator buttons
# Row 2: 7, 8, 9, /,
btn_7 = tk.Button(window, text="7", padx=20, pady=15, command=lambda: button_click(7), font=('Arial', 12))
btn_8 = tk.Button(window, text="8", padx=20, pady=15, command=lambda: button_click(8), font=('Arial', 12))
btn_9 = tk.Button(window, text="9", padx=20, pady=15, command=lambda: button_click(9), font=('Arial', 12))
btn_divide = tk.Button(window, text="/", padx=20, pady=15, command=lambda: button_click("/"), bg='lightgreen', font=('Arial', 12))

btn_7.grid(row=2, column=0, padx=2, pady=2, sticky='nsew')
btn_8.grid(row=2, column=1, padx=2, pady=2, sticky='nsew')
btn_9.grid(row=2, column=2, padx=2, pady=2, sticky='nsew')
btn_divide.grid(row=2, column=3, padx=2, pady=2, sticky='nsew')

# Row 3: 4, 5, 6, *
btn_4 = tk.Button(window, text="4", padx=20, pady=15, command=lambda: button_click(4), font=('Arial', 12))
btn_5 = tk.Button(window, text="5", padx=20, pady=15, command=lambda: button_click(5), font=('Arial', 12))
btn_6 = tk.Button(window, text="6", padx=20, pady=15, command=lambda: button_click(6), font=('Arial', 12))
btn_multiply = tk.Button(window, text="*", padx=20, pady=15, command=lambda: button_click("*"), bg='lightgreen', font=('Arial', 12))

btn_4.grid(row=3, column=0, padx=2, pady=2, sticky='nsew')
btn_5.grid(row=3, column=1, padx=2, pady=2, sticky='nsew')
btn_6.grid(row=3, column=2, padx=2, pady=2, sticky='nsew')
btn_multiply.grid(row=3, column=3, padx=2, pady=2, sticky='nsew')

# Row 4: 1, 2, 3, -
btn_1 = tk.Button(window, text="1", padx=20, pady=15, command=lambda: button_click(1), font=('Arial', 12))
btn_2 = tk.Button(window, text="2", padx=20, pady=15, command=lambda: button_click(2), font=('Arial', 12))
btn_3 = tk.Button(window, text="3", padx=20, pady=15, command=lambda: button_click(3), font=('Arial', 12))
btn_subtract = tk.Button(window, text="-", padx=20, pady=15, command=lambda: button_click("-"), bg='lightgreen', font=('Arial', 12))

btn_1.grid(row=4, column=0, padx=2, pady=2, sticky='nsew')
btn_2.grid(row=4, column=1, padx=2, pady=2, sticky='nsew')
btn_3.grid(row=4, column=2, padx=2, pady=2, sticky='nsew')
btn_subtract.grid(row=4, column=3, padx=2, pady=2, sticky='nsew')

# Row 5: 0, ., =, +
btn_0 = tk.Button(window, text="0", padx=20, pady=15, command=lambda: button_click(0), font=('Arial', 12))
btn_dot = tk.Button(window, text=".", padx=20, pady=15, command=lambda: button_click("."), font=('Arial', 12))
btn_equal = tk.Button(window, text="=", padx=20, pady=15, command=button_equal, bg='yellow', font=('Arial', 12, 'bold'))
btn_add = tk.Button(window, text="+", padx=20, pady=15, command=lambda: button_click("+"), bg='lightgreen', font=('Arial', 12))

btn_0.grid(row=5, column=0, padx=2, pady=2, sticky='nsew')
btn_dot.grid(row=5, column=1, padx=2, pady=2, sticky='nsew')
btn_equal.grid(row=5, column=2, padx=2, pady=2, sticky='nsew')
btn_add.grid(row=5, column=3, padx=2, pady=2, sticky='nsew')

# Configure row weights for responsive layout
for i in range(6):
    window.grid_rowconfigure(i, weight=1)

# Main loop
window.mainloop()