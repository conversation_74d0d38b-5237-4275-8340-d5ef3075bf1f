<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { margin: 5px; padding: 10px; }
        #display { width: 200px; padding: 10px; font-size: 18px; text-align: right; }
    </style>
</head>
<body>
    <h1>Calculator Memory Bug Test</h1>
    
    <div>
        <input type="text" id="display" readonly>
        <br>
        <button onclick="appendToDisplay('5')">5</button>
        <button onclick="memoryAdd()">M+</button>
        <button onclick="appendToDisplay('3')">3</button>
        <button onclick="appendToDisplay('+')">+</button>
        <button onclick="appendToDisplay('2')">2</button>
        <button onclick="calculate()">=</button>
        <button onclick="clearDisplay()">Clear</button>
        <button onclick="memoryRecall()">MR</button>
        <button onclick="memoryClear()">MC</button>
    </div>
    
    <div>
        <button onclick="runTest()">Run Automated Test</button>
        <div id="test-results"></div>
    </div>

    <script>
        const display = document.getElementById('display');
        let shouldClearOnNextInput = false;
        let memory = 0;

        // Basic operations (copied from main calculator with fix)
        function appendToDisplay(value) {
            if (display.value === 'Error') {
                clearDisplay();
            }

            const isOperator = ['+', '-', '*', '/', '**'].includes(value);

            if (shouldClearOnNextInput) {
                if (!isOperator) {
                    clearDisplay();
                }
                shouldClearOnNextInput = false;
            }

            if (value === '.' && display.value.includes('.')) {
                return;
            }

            display.value += value;
        }

        function clearDisplay() {
            display.value = '';
        }

        // Memory functions (with the fix)
        function memoryAdd() {
            try {
                memory += parseFloat(display.value) || 0;
                shouldClearOnNextInput = true;  // This is the fix!
            } catch (error) {
                display.value = 'Error';
            }
        }

        function calculate() {
            try {
                const result = new Function('return ' + display.value)();
                display.value = Number.isFinite(result) ? result : 'Error';
                shouldClearOnNextInput = true;
            } catch (error) {
                display.value = 'Error';
            }
        }

        function memoryClear() {
            memory = 0;
        }

        function memoryRecall() {
            display.value = memory;
            shouldClearOnNextInput = true;
        }

        // Test function
        function runTest() {
            const results = document.getElementById('test-results');
            results.innerHTML = '';
            
            // Test 1: Basic memory bug test
            clearDisplay();
            memoryClear();
            
            appendToDisplay('5');
            const step1 = display.value;
            
            memoryAdd();
            const step2 = display.value;
            const memoryAfterAdd = memory;
            
            appendToDisplay('3');
            const step3 = display.value;
            
            // Results
            const test1Pass = step3 === '3' && memoryAfterAdd === 5;
            results.innerHTML += `
                <div class="test-result ${test1Pass ? 'pass' : 'fail'}">
                    <strong>Test 1 - Memory Bug Fix:</strong><br>
                    Step 1 (enter 5): "${step1}" ✓<br>
                    Step 2 (M+): display="${step2}", memory=${memoryAfterAdd} ✓<br>
                    Step 3 (enter 3): "${step3}" ${step3 === '3' ? '✓' : '✗ (should be "3", not "53")'}<br>
                    <strong>Result: ${test1Pass ? 'PASS' : 'FAIL'}</strong>
                </div>
            `;
            
            // Test 2: Memory operator bug test
            clearDisplay();
            memoryClear();

            appendToDisplay('5');
            const op_step1 = display.value;

            memoryAdd();
            const op_step2 = display.value;
            const op_memoryAfterAdd = memory;

            appendToDisplay('+');
            const op_step3 = display.value;

            appendToDisplay('2');
            const op_step4 = display.value;

            // The display should be "5+2", not "+2"
            const test2Pass = op_step4 === '5+2' && op_memoryAfterAdd === 5;
            results.innerHTML += `
                <div class="test-result ${test2Pass ? 'pass' : 'fail'}">
                    <strong>Test 2 - Memory Operator Bug Fix:</strong><br>
                    Step 1 (enter 5): "${op_step1}" ✓<br>
                    Step 2 (M+): display="${op_step2}", memory=${op_memoryAfterAdd} ✓<br>
                    Step 3 (enter +): "${op_step3}" ${op_step3 === '5+' ? '✓' : '✗'}<br>
                    Step 4 (enter 2): "${op_step4}" ${op_step4 === '5+2' ? '✓' : '✗ (should be "5+2", not "+2")'}<br>
                    <strong>Result: ${test2Pass ? 'PASS' : 'FAIL'}</strong>
                </div>
            `;

            // Test 3: Memory recall test
            clearDisplay();
            memoryRecall();
            const recallValue = display.value;

            const test3Pass = recallValue == '5';
            results.innerHTML += `
                <div class="test-result ${test3Pass ? 'pass' : 'fail'}">
                    <strong>Test 3 - Memory Recall:</strong><br>
                    Memory recall shows: "${recallValue}" ${test3Pass ? '✓' : '✗'}<br>
                    <strong>Result: ${test3Pass ? 'PASS' : 'FAIL'}</strong>
                </div>
            `;
        }
    </script>
</body>
</html>
