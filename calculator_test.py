class Calculator:
    def __init__(self):
        self.display = ''
        self.memory = 0
        self.should_clear_on_next_input = False
        self.last_input_was_memory_add = False
    
    def append_to_display(self, value):
        if self.display == 'Error':
            self.display = ''
        
        is_operator = value in ['+', '-', '*', '/']
        
        if self.last_input_was_memory_add or self.should_clear_on_next_input:
            if not is_operator:
                self.display = ''
            self.last_input_was_memory_add = False
            self.should_clear_on_next_input = False
        
        if value == '.' and '.' in self.display.split('+/-*/')[-1]:
            return
        
        self.display += value
    
    def clear_display(self):
        self.display = ''
        self.should_clear_on_next_input = False
        self.last_input_was_memory_add = False
    
    def calculate(self):
        if not self.display or self.display[-1] in ['+', '-', '*', '/']:
            return
        try:
            result = eval(self.display)
            if isinstance(result, (int, float)):
                # Format the number to remove trailing zeros
                display_num = str(float(result))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = display_num
            else:
                self.display = 'Error'
            self.should_clear_on_next_input = True
            self.last_input_was_memory_add = False
        except:
            self.display = 'Error'
    
    def percentage(self):
        try:
            import re
            match = re.search(r'([\d.]+)$', self.display)
            if match:
                last_number = float(match.group(1))
                prefix = self.display[:-len(match.group(1))]
                percent_value = last_number / 100
                # Format the number to remove trailing zeros
                display_num = str(float(percent_value))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = f"{prefix}{display_num}"
                self.should_clear_on_next_input = True
                self.last_input_was_memory_add = False
        except:
            self.display = 'Error'
    
    def memory_add(self):
        try:
            parts = self.display.split('+/-*/')
            if parts:
                last_number = float(parts[-1])
                self.memory += last_number
                self.last_input_was_memory_add = True
                self.should_clear_on_next_input = True
        except:
            self.display = 'Error'
    
    def memory_clear(self):
        self.memory = 0
    
    def memory_recall(self):
        # Format the number to remove trailing zeros
        display_num = str(float(self.memory))
        if display_num.endswith('.0'):
            display_num = display_num[:-2]
        self.display = display_num
        self.should_clear_on_next_input = True
        self.last_input_was_memory_add = False

def test_percentage_operations():
    calc = Calculator()
    
    # Test case 1: Basic percentage
    calc.append_to_display('50')
    calc.percentage()
    assert calc.display == '0.5', f"Test case 1 failed: {calc.display} != 0.5"
    
    # Test case 2: Percentage with calculation
    calc.clear_display()
    calc.append_to_display('50')
    calc.percentage()
    calc.append_to_display('+')
    calc.append_to_display('2')
    calc.calculate()
    assert calc.display == '2.5', f"Test case 2 failed: {calc.display} != 2.5"
    
    # Test case 3: Percentage followed by new number
    calc.clear_display()
    calc.append_to_display('50')
    calc.percentage()
    calc.append_to_display('4')
    assert calc.display == '4', f"Test case 3 failed: {calc.display} != 4"
    
    print("All percentage tests passed!")

def test_memory_operations():
    calc = Calculator()
    
    # Test case 1: Basic memory add
    calc.append_to_display('30')
    calc.memory_add()
    calc.memory_recall()
    assert calc.display == '30', f"Test case 1 failed: {calc.display} != 30"
    
    # Test case 2: Memory add with calculation
    calc.clear_display()
    calc.append_to_display('30')
    calc.memory_add()
    calc.append_to_display('+')
    calc.append_to_display('5')
    calc.calculate()
    assert calc.display == '35', f"Test case 2 failed: {calc.display} != 35"
    
    # Test case 3: Memory add followed by new number
    calc.clear_display()
    calc.append_to_display('30')
    calc.memory_add()
    calc.append_to_display('4')
    assert calc.display == '4', f"Test case 3 failed: {calc.display} != 4"
    
    print("All memory tests passed!")

if __name__ == '__main__':
    test_percentage_operations()
    test_memory_operations()