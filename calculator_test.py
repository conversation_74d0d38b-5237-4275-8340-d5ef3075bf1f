class Calculator:
    def __init__(self):
        self.display = ''
        self.memory = 0
        self.should_clear_on_next_input = False
        self.last_input_was_memory_add = False
    
    def append_to_display(self, value):
        if self.display == 'Error':
            self.display = ''
        
        is_operator = value in ['+', '-', '*', '/']
        
        if self.last_input_was_memory_add or self.should_clear_on_next_input:
            if not is_operator:
                self.display = ''
            self.last_input_was_memory_add = False
            self.should_clear_on_next_input = False
        
        if value == '.' and '.' in self.display.split('+/-*/')[-1]:
            return
        
        self.display += value
    
    def clear_display(self):
        self.display = ''
        self.should_clear_on_next_input = False
        self.last_input_was_memory_add = False
    
    def calculate(self):
        if not self.display or self.display[-1] in ['+', '-', '*', '/']:
            return
        try:
            result = eval(self.display)
            if isinstance(result, (int, float)):
                # Format the number to remove trailing zeros
                display_num = str(float(result))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = display_num
            else:
                self.display = 'Error'
            self.should_clear_on_next_input = True
            self.last_input_was_memory_add = False
        except:
            self.display = 'Error'
    
    def percentage(self):
        try:
            import re
            match = re.search(r'([\d.]+)$', self.display)
            if match:
                last_number = float(match.group(1))
                prefix = self.display[:-len(match.group(1))]
                percent_value = last_number / 100
                # Format the number to remove trailing zeros
                display_num = str(float(percent_value))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = f"{prefix}{display_num}"
                self.should_clear_on_next_input = True
                self.last_input_was_memory_add = False
        except:
            self.display = 'Error'
    
    def memory_add(self):
        try:
            parts = self.display.split('+/-*/')
            if parts:
                last_number = float(parts[-1])
                self.memory += last_number
                self.last_input_was_memory_add = True
                self.should_clear_on_next_input = True
        except:
            self.display = 'Error'
    
    def memory_clear(self):
        self.memory = 0
    
    def memory_recall(self):
        # Format the number to remove trailing zeros
        display_num = str(float(self.memory))
        if display_num.endswith('.0'):
            display_num = display_num[:-2]
        self.display = display_num
        self.should_clear_on_next_input = True
        self.last_input_was_memory_add = False

def test_percentage_operations():
    calc = Calculator()
    
    # Test case 1: Basic percentage
    calc.append_to_display('50')
    calc.percentage()
    assert calc.display == '0.5', f"Test case 1 failed: {calc.display} != 0.5"
    
    # Test case 2: Percentage with calculation
    calc.clear_display()
    calc.append_to_display('50')
    calc.percentage()
    calc.append_to_display('+')
    calc.append_to_display('2')
    calc.calculate()
    assert calc.display == '2.5', f"Test case 2 failed: {calc.display} != 2.5"
    
    # Test case 3: Percentage followed by new number
    calc.clear_display()
    calc.append_to_display('50')
    calc.percentage()
    calc.append_to_display('4')
    assert calc.display == '4', f"Test case 3 failed: {calc.display} != 4"
    
    print("All percentage tests passed!")

def test_memory_operations():
    calc = Calculator()
    
    # Test case 1: Basic memory add
    calc.append_to_display('30')
    calc.memory_add()
    calc.memory_recall()
    assert calc.display == '30', f"Test case 1 failed: {calc.display} != 30"
    
    # Test case 2: Memory add with calculation
    calc.clear_display()
    calc.append_to_display('30')
    calc.memory_add()
    calc.append_to_display('+')
    calc.append_to_display('5')
    calc.calculate()
    assert calc.display == '35', f"Test case 2 failed: {calc.display} != 35"
    
    # Test case 3: Memory add followed by new number
    calc.clear_display()
    calc.append_to_display('30')
    calc.memory_add()
    calc.append_to_display('4')
    assert calc.display == '4', f"Test case 3 failed: {calc.display} != 4"
    
    print("All memory tests passed!")

def test_memory_bug():
    """Test the specific bug: num1, M+, num2 should show num2, not num1num2"""
    calc = Calculator()

    # Test the bug scenario: 5, M+, 3 should display "3", not "53"
    calc.append_to_display('5')
    print(f"After entering 5: display = '{calc.display}'")

    calc.memory_add()
    print(f"After M+: display = '{calc.display}', memory = {calc.memory}")

    calc.append_to_display('3')
    print(f"After entering 3: display = '{calc.display}'")

    # The display should be "3", not "53"
    expected_display = '3'
    expected_memory = 5.0

    assert calc.display == expected_display, f"Display bug: expected '{expected_display}', got '{calc.display}'"
    assert calc.memory == expected_memory, f"Memory bug: expected {expected_memory}, got {calc.memory}"

    print("Memory bug test passed!")

def test_comprehensive_calculator():
    """Comprehensive test of calculator functionality"""
    calc = Calculator()

    print("\n=== Comprehensive Calculator Tests ===")

    # Test 1: Basic arithmetic
    calc.clear_display()
    calc.append_to_display('2')
    calc.append_to_display('+')
    calc.append_to_display('3')
    calc.calculate()
    assert calc.display == '5', f"Basic addition failed: {calc.display}"
    print("✓ Basic arithmetic test passed")

    # Test 2: Memory operations sequence
    calc.clear_display()
    calc.memory_clear()

    # Add 10 to memory
    calc.append_to_display('10')
    calc.memory_add()

    # Add 5 to memory
    calc.append_to_display('5')
    calc.memory_add()

    # Recall memory (should be 15)
    calc.memory_recall()
    assert calc.display == '15', f"Memory operations failed: {calc.display}"
    print("✓ Memory operations test passed")

    # Test 3: Error handling
    calc.clear_display()
    calc.append_to_display('1')
    calc.append_to_display('/')
    calc.append_to_display('0')
    calc.calculate()
    # Should handle division by zero gracefully
    print(f"✓ Error handling test: division by zero shows '{calc.display}'")

    print("All comprehensive tests passed!")

if __name__ == '__main__':
    test_percentage_operations()
    test_memory_operations()
    test_memory_bug()
    test_comprehensive_calculator()