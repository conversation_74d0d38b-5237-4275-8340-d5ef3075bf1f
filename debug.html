<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Calculator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Calculator Debug Page</h1>
    
    <div class="debug">
        <h3>Loading script.js...</h3>
        <p id="load-status">Loading...</p>
    </div>
    
    <div class="debug">
        <h3>Function Check:</h3>
        <div id="function-check"></div>
    </div>
    
    <div class="debug">
        <h3>Simple Test:</h3>
        <input type="text" id="display" readonly style="width: 200px; padding: 5px;">
        <br><br>
        <button onclick="testFunction('appendToDisplay', '5')">Test appendToDisplay('5')</button>
        <button onclick="testFunction('clearDisplay')">Test clearDisplay()</button>
        <button onclick="testFunction('calculate')">Test calculate()</button>
        <div id="test-results"></div>
    </div>

    <script src="script.js"></script>
    <script>
        window.addEventListener('load', function() {
            const loadStatus = document.getElementById('load-status');
            const functionCheck = document.getElementById('function-check');
            
            // Check if script loaded
            if (typeof appendToDisplay === 'function') {
                loadStatus.innerHTML = '✅ script.js loaded successfully!';
                loadStatus.parentElement.className = 'debug success';
            } else {
                loadStatus.innerHTML = '❌ script.js failed to load!';
                loadStatus.parentElement.className = 'debug error';
            }
            
            // Check all required functions
            const requiredFunctions = [
                'appendToDisplay', 'clearDisplay', 'calculate', 'percentage',
                'memoryAdd', 'memoryRecall', 'memoryClear', 'sqrt', 'power'
            ];
            
            let functionResults = [];
            requiredFunctions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                functionResults.push(`${funcName}: ${exists ? '✅' : '❌'}`);
            });
            
            functionCheck.innerHTML = functionResults.join('<br>');
            
            // Check if display element exists
            const displayElement = document.getElementById('display');
            if (displayElement) {
                functionResults.push(`display element: ✅`);
            } else {
                functionResults.push(`display element: ❌`);
            }
            
            functionCheck.innerHTML = functionResults.join('<br>');
        });
        
        function testFunction(funcName, ...args) {
            const results = document.getElementById('test-results');
            try {
                if (typeof window[funcName] === 'function') {
                    window[funcName](...args);
                    results.innerHTML += `<p>✅ ${funcName}(${args.join(', ')}) executed successfully</p>`;
                } else {
                    results.innerHTML += `<p>❌ ${funcName} is not a function</p>`;
                }
            } catch (error) {
                results.innerHTML += `<p>❌ Error calling ${funcName}: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
