#!/usr/bin/env python3
"""
Test script to verify the updated Tkinter calculator functionality.
This tests the calculator logic without the GUI.
"""

import sys
import os

# Add the current directory to the path to import calculator functions
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the calculator functions (we'll need to modify calculator.py slightly for this)
# For now, let's create a test version of the calculator logic

import re

class TkinterCalculatorLogic:
    """Test version of the Tkinter calculator logic"""
    
    def __init__(self):
        self.display = ""
        self.memory = 0
        self.should_clear_on_next_input = False
        self.last_input_was_memory_add = False
    
    def append_to_display(self, value):
        """Append value to display with proper state management"""
        if self.display == 'Error':
            self.display = ''
        
        is_operator = str(value) in ['+', '-', '*', '/']
        
        if self.last_input_was_memory_add or self.should_clear_on_next_input:
            if not is_operator:
                self.display = ''
            self.last_input_was_memory_add = False
            self.should_clear_on_next_input = False
        
        # Prevent multiple decimal points in a single number
        if str(value) == '.' and '.' in self.display.split('+/-*/')[-1]:
            return
        
        self.display += str(value)
    
    def clear(self):
        """Clear display and reset all state"""
        self.display = ""
        self.should_clear_on_next_input = False
        self.last_input_was_memory_add = False
    
    def calculate(self):
        """Calculate the expression in the display"""
        if not self.display or self.display[-1] in ['+', '-', '*', '/']:
            return
        
        try:
            result = eval(self.display)
            if isinstance(result, (int, float)):
                # Format the number to remove trailing zeros
                display_num = str(float(result))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = display_num
            else:
                self.display = "Error"
            self.should_clear_on_next_input = True
            self.last_input_was_memory_add = False
        except:
            self.display = "Error"
    
    def percentage(self):
        """Improved percentage function with three modes"""
        try:
            current_display = self.display
            
            # Case 1: Simple number followed by % (e.g., "50" -> "0.5")
            if re.match(r'^[\d.]+$', current_display):
                number = float(current_display)
                percent_value = number / 100
                # Format the number to remove trailing zeros
                display_num = str(float(percent_value))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = display_num
                self.should_clear_on_next_input = True
                self.last_input_was_memory_add = False
                return
            
            # Case 2: Expression ending with operator and number
            match = re.search(r'^(.+)([\+\-\*\/])([\d.]+)$', current_display)
            if match:
                base_expr = match.group(1)
                operator = match.group(2)
                last_number = float(match.group(3))
                
                if operator == '+':
                    percent_multiplier = 1 + (last_number / 100)
                    new_expr = f"{base_expr}*{percent_multiplier}"
                elif operator == '-':
                    percent_multiplier = 1 - (last_number / 100)
                    new_expr = f"{base_expr}*{percent_multiplier}"
                elif operator == '*':
                    percent_value = last_number / 100
                    new_expr = f"{base_expr}*{percent_value}"
                elif operator == '/':
                    percent_value = last_number / 100
                    new_expr = f"{base_expr}/{percent_value}"
                
                self.display = new_expr
                self.should_clear_on_next_input = True
                self.last_input_was_memory_add = False
                return
            
            # Case 3: Fallback
            match = re.search(r'([\d.]+)$', current_display)
            if match:
                last_number = float(match.group(1))
                prefix = current_display[:-len(match.group(1))]
                percent_value = last_number / 100
                display_num = str(float(percent_value))
                if display_num.endswith('.0'):
                    display_num = display_num[:-2]
                self.display = f"{prefix}{display_num}"
                self.should_clear_on_next_input = True
                self.last_input_was_memory_add = False
        except:
            self.display = "Error"
    
    def memory_add(self):
        """Add current display value to memory"""
        try:
            current_display = self.display
            
            # If there's an expression, evaluate it first
            if any(op in current_display for op in ['+', '-', '*', '/']):
                self.calculate()
                current_display = self.display
            
            # Add to memory
            if current_display and current_display != 'Error':
                self.memory += float(current_display)
                self.last_input_was_memory_add = True
                self.should_clear_on_next_input = True
        except:
            self.display = "Error"
    
    def memory_recall(self):
        """Recall value from memory"""
        display_num = str(float(self.memory))
        if display_num.endswith('.0'):
            display_num = display_num[:-2]
        self.display = display_num
        self.should_clear_on_next_input = True
        self.last_input_was_memory_add = False
    
    def memory_clear(self):
        """Clear memory"""
        self.memory = 0

def test_tkinter_calculator():
    """Test the Tkinter calculator logic"""
    print("=" * 60)
    print("TESTING UPDATED TKINTER CALCULATOR")
    print("=" * 60)
    
    calc = TkinterCalculatorLogic()
    
    # Test 1: Basic percentage
    print("\n1. Testing Basic Percentage (50% = 0.5)")
    calc.clear()
    calc.append_to_display('5')
    calc.append_to_display('0')
    calc.percentage()
    assert calc.display == '0.5', f"Failed: {calc.display} != 0.5"
    print(f"✓ 50% = {calc.display}")
    
    # Test 2: Addition with percentage
    print("\n2. Testing Addition with Percentage (100 + 20% = 120)")
    calc.clear()
    calc.append_to_display('1')
    calc.append_to_display('0')
    calc.append_to_display('0')
    calc.append_to_display('+')
    calc.append_to_display('2')
    calc.append_to_display('0')
    calc.percentage()
    calc.calculate()
    assert calc.display == '120', f"Failed: {calc.display} != 120"
    print(f"✓ 100 + 20% = {calc.display}")
    
    # Test 3: Memory operations
    print("\n3. Testing Memory Operations")
    calc.clear()
    calc.memory_clear()
    calc.append_to_display('5')
    calc.memory_add()
    calc.append_to_display('3')
    assert calc.display == '3', f"Failed: {calc.display} != 3"
    calc.memory_recall()
    assert calc.display == '5', f"Failed: {calc.display} != 5"
    print(f"✓ Memory operations working correctly")
    
    # Test 4: Memory with operator bug fix
    print("\n4. Testing Memory + Operator Bug Fix")
    calc.clear()
    calc.memory_clear()
    calc.append_to_display('5')
    calc.memory_add()
    calc.append_to_display('+')
    calc.append_to_display('2')
    assert calc.display == '5+2', f"Failed: {calc.display} != 5+2"
    calc.calculate()
    assert calc.display == '7', f"Failed: {calc.display} != 7"
    print(f"✓ Memory + operator bug fixed: 5 M+ + 2 = {calc.display}")
    
    print("\n" + "=" * 60)
    print("ALL TKINTER CALCULATOR TESTS PASSED!")
    print("=" * 60)

if __name__ == '__main__':
    test_tkinter_calculator()
