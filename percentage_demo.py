#!/usr/bin/env python3
"""
Demonstration of the improved percentage functionality in the calculator.
Shows the three different percentage modes:
1. num1 % -> num1/100
2. num1 %, +, num2 -> num1/100 + num2  
3. num1 +/- num2 % -> num1 * (1 +/- num2/100)
"""

from calculator_test import Calculator

def demo_percentage_functions():
    print("=" * 60)
    print("CALCULATOR PERCENTAGE FUNCTION DEMONSTRATION")
    print("=" * 60)
    
    calc = Calculator()
    
    print("\n1. SIMPLE PERCENTAGE (num1 % -> num1/100)")
    print("-" * 40)
    
    # Demo 1: 50% = 0.5
    calc.clear_display()
    calc.append_to_display('50')
    print(f"Input: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    print(f"Result: 50% = {calc.display}")
    
    # Demo 2: 25% = 0.25
    calc.clear_display()
    calc.append_to_display('25')
    print(f"\nInput: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    print(f"Result: 25% = {calc.display}")
    
    print("\n2. PERCENTAGE THEN OPERATION (num1 %, +, num2 -> num1/100 + num2)")
    print("-" * 60)
    
    # Demo 3: 50% + 2 = 2.5
    calc.clear_display()
    calc.append_to_display('50')
    calc.percentage()
    calc.append_to_display('+')
    calc.append_to_display('2')
    print(f"Expression: 50% + 2 = {calc.display}")
    calc.calculate()
    print(f"Result: {calc.display}")
    
    print("\n3. PERCENTAGE OF OPERATION (Advanced Mode)")
    print("-" * 45)
    
    # Demo 4: 100 + 20% = 100 * (1 + 20/100) = 120
    calc.clear_display()
    calc.append_to_display('100')
    calc.append_to_display('+')
    calc.append_to_display('20')
    print(f"Input: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"Result: 100 + 20% = {calc.display} (100 increased by 20%)")
    
    # Demo 5: 100 - 20% = 100 * (1 - 20/100) = 80
    calc.clear_display()
    calc.append_to_display('100')
    calc.append_to_display('-')
    calc.append_to_display('20')
    print(f"\nInput: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"Result: 100 - 20% = {calc.display} (100 decreased by 20%)")
    
    # Demo 6: 200 * 15% = 200 * (15/100) = 30
    calc.clear_display()
    calc.append_to_display('200')
    calc.append_to_display('*')
    calc.append_to_display('15')
    print(f"\nInput: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"Result: 200 * 15% = {calc.display} (15% of 200)")
    
    # Demo 7: 50 / 25% = 50 / (25/100) = 200
    calc.clear_display()
    calc.append_to_display('50')
    calc.append_to_display('/')
    calc.append_to_display('25')
    print(f"\nInput: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"Result: 50 / 25% = {calc.display} (50 divided by 25%)")
    
    print("\n" + "=" * 60)
    print("REAL-WORLD EXAMPLES")
    print("=" * 60)
    
    # Real-world example 1: Price with tax
    calc.clear_display()
    calc.append_to_display('100')
    calc.append_to_display('+')
    calc.append_to_display('8.5')
    print(f"\nPrice + Tax: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"$100 item with 8.5% tax = ${calc.display}")
    
    # Real-world example 2: Discount
    calc.clear_display()
    calc.append_to_display('200')
    calc.append_to_display('-')
    calc.append_to_display('25')
    print(f"\nPrice - Discount: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"$200 item with 25% discount = ${calc.display}")
    
    # Real-world example 3: Tip calculation
    calc.clear_display()
    calc.append_to_display('45.50')
    calc.append_to_display('*')
    calc.append_to_display('18')
    print(f"\nBill * Tip: {calc.display}")
    calc.percentage()
    print(f"After %: {calc.display}")
    calc.calculate()
    print(f"18% tip on $45.50 bill = ${calc.display}")
    
    print("\n" + "=" * 60)
    print("DEMONSTRATION COMPLETE!")
    print("=" * 60)

if __name__ == '__main__':
    demo_percentage_functions()
