const display = document.getElementById('display');
let memory = 0;

// Flag for post-calculation state (after '=' or '%').
// If true, the next number input will clear the display.
let shouldClearOnNextInput = false;

// Special flag ONLY for M+.
// If true, the next number input will clear the display,
// but an operator will not.
let lastInputWasMemoryAdd = false;

/**
 * Appends a value to the calculator display.
 * @param {string} value The character to append.
 */
function appendToDisplay(value) {
    if (display.value === 'Error') {
        display.value = '';
    }

    const isOperator = ['+', '-', '*', '/'].includes(value);

    // Handle clearing logic based on previous state
    if (lastInputWasMemoryAdd) {
        // After M+, operators should NOT clear display, but numbers should
        if (!isOperator) {
            display.value = '';
        }
        lastInputWasMemoryAdd = false;
    } else if (shouldClearOnNextInput) {
        // After = or %, operators should NOT clear display, but numbers should
        if (!isOperator) {
            display.value = '';
        }
        shouldClearOnNextInput = false;
    }

    // Prevent multiple decimal points in a single number segment.
    if (value === '.' && display.value.split(/[\+\-\*\/]/).pop().includes('.')) {
        return;
    }

    display.value += value;
}

/**
 * Clears the calculator display and all state flags.
 * For the 'C' or 'AC' button.
 */
function clearDisplay() {
    display.value = '';
    shouldClearOnNextInput = false;
    lastInputWasMemoryAdd = false;
}

/**
 * Evaluates the expression in the display.
 */
function calculate() {
    if (!display.value || /[\+\-\*\/]$/.test(display.value)) {
        return;
    }
    try {
        const result = new Function('return ' + display.value)();
        if (Number.isFinite(result)) {
            // Format the number to remove trailing zeros
            const displayNum = Number(result).toString();
            display.value = displayNum.endsWith('.0') ? displayNum.slice(0, -2) : displayNum;
        } else {
            display.value = 'Error';
        }
        // A calculation produces a result, so set the result flag.
        shouldClearOnNextInput = true;
        lastInputWasMemoryAdd = false; // Not in M+ state.
    } catch (error) {
        display.value = 'Error';
    }
}

/**
 * Improved percentage function with three modes:
 * 1. num1 % -> num1/100
 * 2. num1 %, +, num2 -> num1/100 + num2
 * 3. num1 +/- num2 % -> num1 * (1 +/- num2/100)
 */
function percentage() {
    try {
        const currentDisplay = display.value;

        // Case 1: Simple number followed by % (e.g., "50" -> "0.5")
        if (/^[\d.]+$/.test(currentDisplay)) {
            const number = parseFloat(currentDisplay);
            const percentValue = number / 100;
            // Format the number
            const displayNum = Number(percentValue).toString();
            display.value = displayNum.endsWith('.0') ? displayNum.slice(0, -2) : displayNum;
            shouldClearOnNextInput = true;
            lastInputWasMemoryAdd = false;
            return;
        }

        // Case 2: Expression ending with operator and number (e.g., "100+20" -> "100*(1+20/100)")
        const match = currentDisplay.match(/^(.+)([\+\-\*\/])([\d.]+)$/);
        if (match) {
            const baseExpr = match[1];
            const operator = match[2];
            const lastNumber = parseFloat(match[3]);

            if (operator === '+') {
                // num1 + num2 % -> num1 * (1 + num2/100)
                const percentMultiplier = 1 + (lastNumber / 100);
                display.value = `${baseExpr}*${percentMultiplier}`;
            } else if (operator === '-') {
                // num1 - num2 % -> num1 * (1 - num2/100)
                const percentMultiplier = 1 - (lastNumber / 100);
                display.value = `${baseExpr}*${percentMultiplier}`;
            } else if (operator === '*') {
                // num1 * num2 % -> num1 * (num2/100)
                const percentValue = lastNumber / 100;
                display.value = `${baseExpr}*${percentValue}`;
            } else if (operator === '/') {
                // num1 / num2 % -> num1 / (num2/100)
                const percentValue = lastNumber / 100;
                display.value = `${baseExpr}/${percentValue}`;
            }

            shouldClearOnNextInput = true;
            lastInputWasMemoryAdd = false;
            return;
        }

        // Case 3: Fallback - just convert the current value to percentage
        const number = parseFloat(currentDisplay);
        if (!isNaN(number)) {
            const percentValue = number / 100;
            // Format the number
            const displayNum = Number(percentValue).toString();
            display.value = displayNum.endsWith('.0') ? displayNum.slice(0, -2) : displayNum;
            shouldClearOnNextInput = true;
            lastInputWasMemoryAdd = false;
        }
    } catch (error) {
        display.value = 'Error';
    }
}

/**
 * Adds the last number on the display to memory.
 * This does NOT change the display, so it needs its own special flag.
 */
function memoryAdd() {
    try {
        // First try to evaluate any existing expression
        if (display.value.includes('+') || display.value.includes('-') || 
            display.value.includes('*') || display.value.includes('/')) {
            calculate();
        }
        
        // Now add the current number to memory
        const currentValue = parseFloat(display.value);
        if (!isNaN(currentValue)) {
            memory += currentValue;
            lastInputWasMemoryAdd = true;
            shouldClearOnNextInput = true; // Treat M+ like a calculation result
        }
    } catch (error) {
        display.value = 'Error';
    }
}

/**
 * Clears the memory.
 */
function memoryClear() {
    memory = 0;
}

/**
 * Recalls the value from memory and displays it, replacing current content.
 */
function memoryRecall() {
    // Format the number to remove trailing zeros
    const displayNum = Number(memory).toString();
    display.value = displayNum.endsWith('.0') ? displayNum.slice(0, -2) : displayNum;
    // Recalling a number is like getting a result; next number should clear it.
    shouldClearOnNextInput = true;
    lastInputWasMemoryAdd = false;
}

/**
 * Square root function
 */
function sqrt() {
    try {
        const value = parseFloat(display.value);
        if (value >= 0) {
            const result = Math.sqrt(value);
            // Format the number to remove trailing zeros
            const displayNum = Number(result).toString();
            display.value = displayNum.endsWith('.0') ? displayNum.slice(0, -2) : displayNum;
        } else {
            display.value = 'Error';
        }
        shouldClearOnNextInput = true;
        lastInputWasMemoryAdd = false;
    } catch (error) {
        display.value = 'Error';
    }
}

/**
 * Power function (x^y)
 */
function power() {
    try {
        display.value += '**';
        shouldClearOnNextInput = false;
        lastInputWasMemoryAdd = false;
    } catch (error) {
        display.value = 'Error';
    }
}

// Input validation to only allow valid characters from a keyboard.
display.addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/[^\d\+\-\*\/\.]/g, '');
});