<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        #display { 
            width: 100%; 
            padding: 15px; 
            font-size: 24px; 
            text-align: right; 
            border: 2px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            box-sizing: border-box;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }
        button { 
            padding: 15px; 
            font-size: 18px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .number { background-color: #e9e9e9; }
        .operator { background-color: #007bff; color: white; }
        .function { background-color: #28a745; color: white; }
        .memory { background-color: #ffc107; color: black; }
        .equals { background-color: #dc3545; color: white; }
        
        button:hover { opacity: 0.8; }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            background-color: #d4edda;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Calculator Test - Using script.js</h2>
        
        <input type="text" id="display" readonly placeholder="0">
        
        <div class="button-grid">
            <button class="function" onclick="clearDisplay()">C</button>
            <button class="memory" onclick="memoryRecall()">MR</button>
            <button class="memory" onclick="memoryClear()">MC</button>
            <button class="memory" onclick="memoryAdd()">M+</button>
            
            <button class="number" onclick="appendToDisplay('7')">7</button>
            <button class="number" onclick="appendToDisplay('8')">8</button>
            <button class="number" onclick="appendToDisplay('9')">9</button>
            <button class="operator" onclick="appendToDisplay('/')">/</button>
            
            <button class="number" onclick="appendToDisplay('4')">4</button>
            <button class="number" onclick="appendToDisplay('5')">5</button>
            <button class="number" onclick="appendToDisplay('6')">6</button>
            <button class="operator" onclick="appendToDisplay('*')">*</button>
            
            <button class="number" onclick="appendToDisplay('1')">1</button>
            <button class="number" onclick="appendToDisplay('2')">2</button>
            <button class="number" onclick="appendToDisplay('3')">3</button>
            <button class="operator" onclick="appendToDisplay('-')">-</button>
            
            <button class="number" onclick="appendToDisplay('0')">0</button>
            <button class="number" onclick="appendToDisplay('.')">.</button>
            <button class="equals" onclick="calculate()">=</button>
            <button class="operator" onclick="appendToDisplay('+')">+</button>
        </div>
        
        <div style="margin-top: 15px; display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
            <button class="function" onclick="percentage()">%</button>
            <button class="function" onclick="sqrt()">√</button>
            <button class="function" onclick="power()">x^y</button>
        </div>
        
        <div class="status">
            <strong>Status:</strong> Calculator loaded with script.js
        </div>
        
        <div style="margin-top: 15px;">
            <h4>Quick Tests:</h4>
            <button onclick="runQuickTest()" style="width: 100%; padding: 10px; background: #17a2b8; color: white; border: none; border-radius: 5px;">
                Run Quick Test
            </button>
            <div id="test-results" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        function runQuickTest() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p>Running tests...</p>';
            
            let testResults = [];
            
            try {
                // Test 1: Basic calculation
                clearDisplay();
                appendToDisplay('2');
                appendToDisplay('+');
                appendToDisplay('3');
                calculate();
                const test1 = display.value === '5';
                testResults.push(`Test 1 (2+3=5): ${test1 ? '✅ PASS' : '❌ FAIL'} (Result: ${display.value})`);
                
                // Test 2: Percentage
                clearDisplay();
                appendToDisplay('50');
                percentage();
                const test2 = display.value === '0.5';
                testResults.push(`Test 2 (50%=0.5): ${test2 ? '✅ PASS' : '❌ FAIL'} (Result: ${display.value})`);
                
                // Test 3: Memory
                clearDisplay();
                memoryClear();
                appendToDisplay('10');
                memoryAdd();
                appendToDisplay('5');
                const test3a = display.value === '5';
                memoryRecall();
                const test3b = display.value === '10';
                testResults.push(`Test 3 (Memory): ${test3a && test3b ? '✅ PASS' : '❌ FAIL'} (M+ then MR)`);
                
                // Test 4: Square root
                clearDisplay();
                appendToDisplay('9');
                sqrt();
                const test4 = display.value === '3';
                testResults.push(`Test 4 (√9=3): ${test4 ? '✅ PASS' : '❌ FAIL'} (Result: ${display.value})`);
                
                results.innerHTML = '<h5>Test Results:</h5>' + testResults.map(r => `<p>${r}</p>`).join('');
                
            } catch (error) {
                results.innerHTML = `<p style="color: red;">❌ ERROR: ${error.message}</p>`;
            }
        }
        
        // Test if script.js loaded properly
        window.addEventListener('load', function() {
            const status = document.querySelector('.status');
            if (typeof appendToDisplay === 'function') {
                status.innerHTML = '<strong>Status:</strong> ✅ script.js loaded successfully!';
                status.style.backgroundColor = '#d4edda';
            } else {
                status.innerHTML = '<strong>Status:</strong> ❌ script.js failed to load!';
                status.style.backgroundColor = '#f8d7da';
            }
        });
    </script>
</body>
</html>
