<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Specific Bug Test: num1, M+, +, num2, =, num2</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 5px; 
            border-left: 4px solid;
        }
        .pass { 
            background-color: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .fail { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        button { 
            margin: 5px; 
            padding: 10px 15px; 
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .calc-button { background: #e9ecef; }
        .operator { background: #007bff; color: white; }
        .memory { background: #ffc107; }
        .equals { background: #28a745; color: white; }
        .clear { background: #dc3545; color: white; }
        
        #display { 
            width: 100%; 
            padding: 15px; 
            font-size: 24px; 
            text-align: right; 
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 15px 0;
            box-sizing: border-box;
            background: #f8f9fa;
        }
        
        .step {
            background: #e3f2fd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #2196f3;
        }
        
        .debug {
            background: #fff3cd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #ffc107;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Specific Bug Test</h1>
        <p><strong>Bug:</strong> <code>num1, M+, +, num2, =, num2</code> should show just <code>num2</code>, not <code>result+num2</code></p>
        
        <div>
            <h3>Calculator Display:</h3>
            <input type="text" id="display" readonly placeholder="0">
            
            <div style="margin: 15px 0;">
                <button class="calc-button" onclick="appendToDisplay('5')">5</button>
                <button class="calc-button" onclick="appendToDisplay('2')">2</button>
                <button class="calc-button" onclick="appendToDisplay('3')">3</button>
                <button class="operator" onclick="appendToDisplay('+')">+</button>
                <button class="memory" onclick="memoryAdd()">M+</button>
                <button class="equals" onclick="calculate()">=</button>
                <button class="clear" onclick="clearDisplay()">Clear</button>
                <button class="memory" onclick="memoryClear()">MC</button>
            </div>
            
            <div class="debug" id="debug-info">
                Debug info will appear here...
            </div>
        </div>
        
        <div>
            <h3>Manual Test Steps:</h3>
            <div class="step">1. Click "5" → Display should show "5"</div>
            <div class="step">2. Click "M+" → Display should still show "5"</div>
            <div class="step">3. Click "+" → Display should show "5+"</div>
            <div class="step">4. Click "2" → Display should show "5+2"</div>
            <div class="step">5. Click "=" → Display should show "7"</div>
            <div class="step">6. Click "3" → Display should show "3" (NOT "73"!)</div>
        </div>
        
        <div>
            <button onclick="runSpecificTest()" style="width: 100%; padding: 15px; background: #17a2b8; color: white; border: none; border-radius: 5px; font-size: 16px; margin: 20px 0;">
                🧪 Run Specific Bug Test
            </button>
            <div id="test-results"></div>
        </div>
        
        <div>
            <button onclick="debugFlags()" style="width: 100%; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 5px;">
                🔍 Debug Flags
            </button>
        </div>
    </div>

    <script src="script.js?v=4"></script>
    <script>
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = `
                Display: "${display.value}"<br>
                shouldClearOnNextInput: ${calculatorState.shouldClearOnNextInput}<br>
                lastInputWasMemoryAdd: ${calculatorState.lastInputWasMemoryAdd}<br>
                memory: ${calculatorState.memory}
            `;
        }
        
        function debugFlags() {
            updateDebugInfo();
        }
        
        // Override functions to add debugging
        const originalAppendToDisplay = appendToDisplay;
        const originalCalculate = calculate;
        const originalMemoryAdd = memoryAdd;
        
        window.appendToDisplay = function(value) {
            console.log(`Before appendToDisplay(${value}): shouldClearOnNextInput=${calculatorState.shouldClearOnNextInput}, lastInputWasMemoryAdd=${calculatorState.lastInputWasMemoryAdd}`);
            originalAppendToDisplay(value);
            console.log(`After appendToDisplay(${value}): display="${display.value}", shouldClearOnNextInput=${calculatorState.shouldClearOnNextInput}, lastInputWasMemoryAdd=${calculatorState.lastInputWasMemoryAdd}`);
            updateDebugInfo();
        };

        window.calculate = function() {
            console.log(`Before calculate(): shouldClearOnNextInput=${calculatorState.shouldClearOnNextInput}, lastInputWasMemoryAdd=${calculatorState.lastInputWasMemoryAdd}`);
            originalCalculate();
            console.log(`After calculate(): display="${display.value}", shouldClearOnNextInput=${calculatorState.shouldClearOnNextInput}, lastInputWasMemoryAdd=${calculatorState.lastInputWasMemoryAdd}`);
            updateDebugInfo();
        };

        window.memoryAdd = function() {
            console.log(`Before memoryAdd(): shouldClearOnNextInput=${calculatorState.shouldClearOnNextInput}, lastInputWasMemoryAdd=${calculatorState.lastInputWasMemoryAdd}`);
            originalMemoryAdd();
            console.log(`After memoryAdd(): display="${display.value}", shouldClearOnNextInput=${calculatorState.shouldClearOnNextInput}, lastInputWasMemoryAdd=${calculatorState.lastInputWasMemoryAdd}`);
            updateDebugInfo();
        };
        
        function runSpecificTest() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p>🔄 Running specific bug test...</p>';
            
            try {
                // Clear everything first
                clearDisplay();
                memoryClear();
                updateDebugInfo();
                
                // Step 1: Enter 5
                appendToDisplay('5');
                const step1 = display.value;
                
                // Step 2: M+ (memory add)
                memoryAdd();
                const step2 = display.value;
                
                // Step 3: Enter +
                appendToDisplay('+');
                const step3 = display.value;
                
                // Step 4: Enter 2
                appendToDisplay('2');
                const step4 = display.value;
                
                // Step 5: Calculate
                calculate();
                const step5 = display.value;
                
                // Step 6: Enter 3 (THIS IS THE BUG!)
                appendToDisplay('3');
                const step6 = display.value;
                
                // Check results
                const test1Pass = step1 === '5';
                const test2Pass = step2 === '5';
                const test3Pass = step3 === '5+';
                const test4Pass = step4 === '5+2';
                const test5Pass = step5 === '7';
                const test6Pass = step6 === '3'; // Should be just "3", not "73"
                
                const allPass = test1Pass && test2Pass && test3Pass && test4Pass && test5Pass && test6Pass;
                
                results.innerHTML = `
                    <div class="test-result ${allPass ? 'pass' : 'fail'}">
                        <h4>${allPass ? '✅ TEST PASSED!' : '❌ TEST FAILED!'}</h4>
                        <p><strong>Step-by-step results:</strong></p>
                        <ul>
                            <li>Step 1 (enter 5): "${step1}" ${test1Pass ? '✅' : '❌'}</li>
                            <li>Step 2 (M+): "${step2}" ${test2Pass ? '✅' : '❌'}</li>
                            <li>Step 3 (enter +): "${step3}" ${test3Pass ? '✅' : '❌'}</li>
                            <li>Step 4 (enter 2): "${step4}" ${test4Pass ? '✅' : '❌'}</li>
                            <li>Step 5 (=): "${step5}" ${test5Pass ? '✅' : '❌'}</li>
                            <li><strong>Step 6 (enter 3): "${step6}" ${test6Pass ? '✅' : '❌'} (Expected: "3")</strong></li>
                        </ul>
                        <p><strong>Bug Status:</strong> ${test6Pass ? 'FIXED! 🎉' : 'STILL EXISTS 😞'}</p>
                        ${!test6Pass ? '<p><strong>Issue:</strong> After calculation, entering a new number should clear the display!</p>' : ''}
                    </div>
                `;
                
            } catch (error) {
                results.innerHTML = `<div class="test-result fail"><h4>❌ ERROR</h4><p>${error.message}</p></div>`;
            }
        }
        
        // Initialize debug info
        window.addEventListener('load', function() {
            updateDebugInfo();
        });
    </script>
</body>
</html>
