<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator</title>
    <style>
        :root {
          --primary: #3498db;
          --secondary: #2980b9;
          --accent: #e74c3c;
          --light: #ecf0f1;
          --dark: #2c3e50;
          --shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
          --gray: #bdc3c7;
          --dark-gray: #95a5a6;
          --white: #ffffff;
        }

        body {
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          min-height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: 'Segoe UI', system-ui, sans-serif;
          margin: 0;
          padding: 20px;
        }

        .calculator {
          width: 360px;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: var(--shadow);
          background: var(--white);
          transition: transform 0.3s ease;
        }

        .calculator:hover {
          transform: translateY(-5px);
        }

        .calculator input[type="text"] {
          width: calc(100% - 40px);
          padding: 25px 20px;
          font-size: 2.5rem;
          border: none;
          text-align: right;
          color: var(--dark);
          background: var(--white);
          margin: 20px 10px;
          border-radius: 10px;
          box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
          font-weight: 500;
          caret-color: transparent;
          outline: none;
        }

        .calculator .buttons {
          display: grid;
          grid-template-columns: repeat(5, 1fr);
          gap: 8px;
          padding: 15px;
          background: linear-gradient(145deg, #f0f0f0, #ffffff);
        }

        /* DEFAULT BUTTON STYLE */
        .calculator .buttons button {
          padding: 18px;
          font-size: 1.3rem;
          border: none;
          border-radius: 12px;
          background: linear-gradient(to bottom, var(--white), #f8f9fa);
          color: var(--dark);
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
          font-weight: 500;
          position: relative;
          overflow: hidden;
        }

        /* OPERATOR BUTTONS (BLUE) */
        .calculator .buttons button:nth-child(9),   /* / */
        .calculator .buttons button:nth-child(14),  /* * */
        .calculator .buttons button:nth-child(19),  /* - */
        .calculator .buttons button:nth-child(24) { /* + */
          background: linear-gradient(to bottom, var(--primary), var(--secondary));
          color: white;
          font-weight: 600;
        }

        /* FUNCTION BUTTONS (GRAY) */
        .calculator .buttons button:nth-child(1),   /* C */
        .calculator .buttons button:nth-child(2),   /* MR */
        .calculator .buttons button:nth-child(3),   /* MC */
        .calculator .buttons button:nth-child(4),   /* M+ */
        .calculator .buttons button:nth-child(5),   /* √ */
        .calculator .buttons button:nth-child(10),  /* % */
        .calculator .buttons button:nth-child(15) { /* x^y */
          background: linear-gradient(to bottom, var(--gray), var(--dark-gray));
          color: white;
        }

        /* EQUALS BUTTON (RED) */
        .calculator .buttons button:nth-child(22) {
          background: linear-gradient(to bottom, var(--accent), #c0392b);
          color: white;
          font-weight: 600;
        }

        /* HIDDEN PLACEHOLDER BUTTONS */
        .calculator .buttons button:nth-child(20),  /* Empty cell after - */
        .calculator .buttons button:nth-child(25) { /* Empty cell after + */
          visibility: hidden;
          pointer-events: none;
        }

        /* HOVER EFFECTS */
        .calculator .buttons button:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        }

        .calculator .buttons button:active {
          transform: translateY(1px);
        }

        /* RIPPLE EFFECT */
        .calculator .buttons button::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 5px;
          height: 5px;
          background: rgba(255, 255, 255, 0.5);
          opacity: 0;
          border-radius: 100%;
          transform: scale(1, 1) translate(-50%);
          transform-origin: 50% 50%;
        }

        .calculator .buttons button:focus:not(:active)::after {
          animation: ripple 0.6s ease-out;
        }

        @keyframes ripple {
          0% {
            transform: scale(0, 0);
            opacity: 0.5;
          }
          100% {
            transform: scale(20, 20);
            opacity: 0;
          }
        }

        /* RESPONSIVE ADJUSTMENTS */
        @media (max-width: 400px) {
          .calculator {
            width: 100%;
            max-width: 340px;
          }
          
          .calculator input[type="text"] {
            font-size: 2rem;
            padding: 20px 15px;
          }
          
          .calculator .buttons button {
            padding: 15px;
            font-size: 1.1rem;
          }
        }
    </style>
</head>
<body>
    <div class="calculator">
        <input type="text" id="display" readonly>
        <div class="buttons">
            <!-- Row 1 -->
            <button onclick="clearDisplay()">C</button>
            <button onclick="memoryRecall()">MR</button>
            <button onclick="memoryClear()">MC</button>
            <button onclick="memoryAdd()">M+</button>
            <button onclick="sqrt()">√</button>
            
            <!-- Row 2 -->
            <button onclick="appendToDisplay('7')">7</button>
            <button onclick="appendToDisplay('8')">8</button>
            <button onclick="appendToDisplay('9')">9</button>
            <button onclick="appendToDisplay('/')">/</button>
            <button onclick="percentage()">%</button>
            
            <!-- Row 3 -->
            <button onclick="appendToDisplay('4')">4</button>
            <button onclick="appendToDisplay('5')">5</button>
            <button onclick="appendToDisplay('6')">6</button>
            <button onclick="appendToDisplay('*')">*</button>
            <button onclick="power()">x^y</button>
            
            <!-- Row 4 -->
            <button onclick="appendToDisplay('1')">1</button>
            <button onclick="appendToDisplay('2')">2</button>
            <button onclick="appendToDisplay('3')">3</button>
            <button onclick="appendToDisplay('-')">-</button>
            <button style="visibility: hidden;"></button> <!-- Empty cell -->
            
            <!-- Row 5 -->
            <button onclick="appendToDisplay('0')">0</button>
            <button onclick="appendToDisplay('.')">.</button>
            <button onclick="calculate()">=</button>
            <button onclick="appendToDisplay('+')">+</button>
            <button style="visibility: hidden;"></button> <!-- Empty cell -->
        </div>
    </div>
    <script>
        const display = document.getElementById('display');
        let shouldClearOnNextInput = false;
        let memory = 0;

        // Basic operations
        function appendToDisplay(value) {
            if (display.value === 'Error') {
                clearDisplay();
            }

            const isOperator = ['+', '-', '*', '/', '**'].includes(value);

            if (shouldClearOnNextInput) {
                if (!isOperator) {
                    clearDisplay();
                }
                shouldClearOnNextInput = false;
            }

            if (value === '.' && display.value.includes('.')) {
                return;
            }

            display.value += value;
        }

        function clearDisplay() {
            display.value = '';
        }

        function calculate() {
            try {
                const result = new Function('return ' + display.value)();
                display.value = Number.isFinite(result) ? result : 'Error';
                shouldClearOnNextInput = true;
            } catch (error) {
                display.value = 'Error';
            }
        }

        // Advanced functions
        function sqrt() {
            try {
                const value = parseFloat(display.value);
                if (value >= 0) {
                    display.value = Math.sqrt(value);
                } else {
                    display.value = 'Error';
                }
                shouldClearOnNextInput = true;
            } catch (error) {
                display.value = 'Error';
            }
        }

        function percentage() {
            try {
                display.value = parseFloat(display.value) / 100;
                shouldClearOnNextInput = true;
            } catch (error) {
                display.value = 'Error';
            }
        }

        function power() {
            try {
                display.value += '**';
                shouldClearOnNextInput = false;
            } catch (error) {
                display.value = 'Error';
            }
        }

        // Memory functions
        function memoryAdd() {
            try {
                memory += parseFloat(display.value) || 0;
                shouldClearOnNextInput = true;
            } catch (error) {
                display.value = 'Error';
            }
        }

        function memoryClear() {
            memory = 0;
        }

        function memoryRecall() {
            display.value = memory;
            shouldClearOnNextInput = true;
        }

        // Input validation
        display.addEventListener('input', function(e) {
            if (!/^[\d\+\-\*\/\.\*\*]*$/.test(e.target.value)) {
                e.target.value = e.target.value.slice(0, -1);
            }
        });
    </script>
</body>
</html>