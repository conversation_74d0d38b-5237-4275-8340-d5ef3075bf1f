<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory + Operator Bug Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 5px; 
            border-left: 4px solid;
        }
        .pass { 
            background-color: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .fail { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        button { 
            margin: 5px; 
            padding: 10px 15px; 
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .calc-button { background: #e9ecef; }
        .operator { background: #007bff; color: white; }
        .memory { background: #ffc107; }
        .equals { background: #28a745; color: white; }
        .clear { background: #dc3545; color: white; }
        
        #display { 
            width: 100%; 
            padding: 15px; 
            font-size: 24px; 
            text-align: right; 
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 15px 0;
            box-sizing: border-box;
            background: #f8f9fa;
        }
        
        .step {
            background: #e3f2fd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Memory + Operator Bug Test</h1>
        <p><strong>Bug Description:</strong> When doing <code>num1, M+, +, num2, =</code>, it should show <code>num1+num2</code>, not just <code>num2</code></p>
        
        <div>
            <h3>Calculator Display:</h3>
            <input type="text" id="display" readonly placeholder="0">
            
            <div style="margin: 15px 0;">
                <button class="calc-button" onclick="appendToDisplay('5')">5</button>
                <button class="calc-button" onclick="appendToDisplay('2')">2</button>
                <button class="calc-button" onclick="appendToDisplay('3')">3</button>
                <button class="operator" onclick="appendToDisplay('+')">+</button>
                <button class="operator" onclick="appendToDisplay('-')">-</button>
                <button class="memory" onclick="memoryAdd()">M+</button>
                <button class="equals" onclick="calculate()">=</button>
                <button class="clear" onclick="clearDisplay()">Clear</button>
                <button class="memory" onclick="memoryClear()">MC</button>
            </div>
        </div>
        
        <div>
            <h3>Manual Test Steps:</h3>
            <div class="step">1. Click "5" → Display should show "5"</div>
            <div class="step">2. Click "M+" → Display should still show "5"</div>
            <div class="step">3. Click "+" → Display should show "5+"</div>
            <div class="step">4. Click "2" → Display should show "5+2"</div>
            <div class="step">5. Click "=" → Display should show "7" (not "2"!)</div>
        </div>
        
        <div>
            <button onclick="runAutomatedTest()" style="width: 100%; padding: 15px; background: #17a2b8; color: white; border: none; border-radius: 5px; font-size: 16px; margin: 20px 0;">
                🧪 Run Automated Test
            </button>
            <div id="test-results"></div>
        </div>
        
        <div>
            <h3>Additional Test Cases:</h3>
            <button onclick="runAllTests()" style="width: 100%; padding: 10px; background: #6f42c1; color: white; border: none; border-radius: 5px;">
                Run All Memory Bug Tests
            </button>
            <div id="all-test-results"></div>
        </div>
    </div>

    <script src="script.js?v=2"></script>
    <script>
        function runAutomatedTest() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p>🔄 Running test...</p>';
            
            try {
                // Clear everything first
                clearDisplay();
                memoryClear();
                
                // Step 1: Enter 5
                appendToDisplay('5');
                const step1 = display.value;
                
                // Step 2: M+ (memory add)
                memoryAdd();
                const step2 = display.value;
                
                // Step 3: Enter +
                appendToDisplay('+');
                const step3 = display.value;
                
                // Step 4: Enter 2
                appendToDisplay('2');
                const step4 = display.value;
                
                // Step 5: Calculate
                calculate();
                const step5 = display.value;
                
                // Check results
                const test1Pass = step1 === '5';
                const test2Pass = step2 === '5';
                const test3Pass = step3 === '5+';
                const test4Pass = step4 === '5+2';
                const test5Pass = step5 === '7';
                
                const allPass = test1Pass && test2Pass && test3Pass && test4Pass && test5Pass;
                
                results.innerHTML = `
                    <div class="test-result ${allPass ? 'pass' : 'fail'}">
                        <h4>${allPass ? '✅ TEST PASSED!' : '❌ TEST FAILED!'}</h4>
                        <p><strong>Step-by-step results:</strong></p>
                        <ul>
                            <li>Step 1 (enter 5): "${step1}" ${test1Pass ? '✅' : '❌'}</li>
                            <li>Step 2 (M+): "${step2}" ${test2Pass ? '✅' : '❌'}</li>
                            <li>Step 3 (enter +): "${step3}" ${test3Pass ? '✅' : '❌'}</li>
                            <li>Step 4 (enter 2): "${step4}" ${test4Pass ? '✅' : '❌'}</li>
                            <li>Step 5 (=): "${step5}" ${test5Pass ? '✅' : '❌'} (Expected: 7)</li>
                        </ul>
                        <p><strong>Final Result:</strong> ${allPass ? 'Bug is FIXED! 🎉' : 'Bug still exists 😞'}</p>
                    </div>
                `;
                
            } catch (error) {
                results.innerHTML = `<div class="test-result fail"><h4>❌ ERROR</h4><p>${error.message}</p></div>`;
            }
        }
        
        function runAllTests() {
            const results = document.getElementById('all-test-results');
            results.innerHTML = '<p>🔄 Running all tests...</p>';
            
            let allResults = [];
            
            try {
                // Test 1: Basic memory bug (5, M+, 3 should show 3)
                clearDisplay();
                memoryClear();
                appendToDisplay('5');
                memoryAdd();
                appendToDisplay('3');
                const test1Pass = display.value === '3';
                allResults.push(`Test 1 (5, M+, 3 → 3): ${test1Pass ? '✅ PASS' : '❌ FAIL'} (Got: ${display.value})`);
                
                // Test 2: Memory + operator bug (5, M+, +, 2, = should show 7)
                clearDisplay();
                memoryClear();
                appendToDisplay('5');
                memoryAdd();
                appendToDisplay('+');
                appendToDisplay('2');
                calculate();
                const test2Pass = display.value === '7';
                allResults.push(`Test 2 (5, M+, +, 2, = → 7): ${test2Pass ? '✅ PASS' : '❌ FAIL'} (Got: ${display.value})`);
                
                // Test 3: Memory + subtraction (10, M+, -, 3, = should show 7)
                clearDisplay();
                memoryClear();
                appendToDisplay('10');
                memoryAdd();
                appendToDisplay('-');
                appendToDisplay('3');
                calculate();
                const test3Pass = display.value === '7';
                allResults.push(`Test 3 (10, M+, -, 3, = → 7): ${test3Pass ? '✅ PASS' : '❌ FAIL'} (Got: ${display.value})`);
                
                // Test 4: Memory + multiplication (6, M+, *, 2, = should show 12)
                clearDisplay();
                memoryClear();
                appendToDisplay('6');
                memoryAdd();
                appendToDisplay('*');
                appendToDisplay('2');
                calculate();
                const test4Pass = display.value === '12';
                allResults.push(`Test 4 (6, M+, *, 2, = → 12): ${test4Pass ? '✅ PASS' : '❌ FAIL'} (Got: ${display.value})`);
                
                const allPass = test1Pass && test2Pass && test3Pass && test4Pass;
                
                results.innerHTML = `
                    <div class="test-result ${allPass ? 'pass' : 'fail'}">
                        <h4>${allPass ? '✅ ALL TESTS PASSED!' : '❌ SOME TESTS FAILED!'}</h4>
                        ${allResults.map(r => `<p>${r}</p>`).join('')}
                    </div>
                `;
                
            } catch (error) {
                results.innerHTML = `<div class="test-result fail"><h4>❌ ERROR</h4><p>${error.message}</p></div>`;
            }
        }
    </script>
</body>
</html>
